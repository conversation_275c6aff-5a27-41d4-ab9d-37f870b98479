from apps.connectors.integrations.vendors.nozomi.nozomi_vantage.v1.api import (
    NozomiVantageV1Api,
)
from apps.connectors.integrations.vendors.nozomi.nozomi_vantage.v1.health_check import (
    ConnectionHealthCheck,
)
from apps.connectors.integrations.vendors.nozomi.nozomi_vantage.v1.integration import (
    NozomiVantageV1Integration,
)
from apps.connectors.tests.integrations.base import (
    BaseIntegrationTest,
    HealthCheckComponentTestMixin,
)
from apps.tests.base import BaseTestCase


class NozomiVantageV1ApiTest(BaseTestCase):
    def test_api_init(self):
        """Test NozomiVantageV1Api initialization"""
        api = NozomiVantageV1Api(
            url="https://example.com", api_key_name="test", api_key_token="token"
        )
        self.assertIsInstance(api, NozomiVantageV1Api)


class NozomiVantageV1IntegrationTest(BaseIntegrationTest):
    pass


class NozomiVantageV1HealthCheckTest(BaseTestCase):
    def test_connection_health_check_not_implemented(self):
        """Test ConnectionHealthCheck raises NotImplementedError and calls get_api"""
        from unittest.mock import patch

        from apps.connectors.integrations.vendors.nozomi.nozomi_vantage.v1.connection import (
            NozomiVantageV1Config,
        )
        from apps.connectors.integrations.vendors.nozomi.nozomi_vantage.v1.settings import (
            NozomiVantageV1Settings,
        )

        # Mock the encryption service to avoid AWS token issues
        with patch(
            "apps.connectors.integrations.types.encryption_service.encrypt"
        ) as mock_encrypt:
            mock_encrypt.return_value = "encrypted_token"

            # Create mock config and settings
            config = NozomiVantageV1Config(
                url="https://example.com",
                api_key_name="test_key",
                api_key_token="test_token",
            )
            settings = NozomiVantageV1Settings({})

            # Create integration with required parameters
            integration = NozomiVantageV1Integration(config=config, settings=settings)
            health_check = ConnectionHealthCheck(integration)

            # Mock the get_api method to ensure it's called
            with patch.object(integration, "get_api") as mock_get_api:
                with self.assertRaises(NotImplementedError):
                    health_check.get_result()
                # Verify that get_api was called
                mock_get_api.assert_called_once()


class NozomiVantageV1HealthCheckComponentsTest(
    BaseTestCase, HealthCheckComponentTestMixin
):
    pass
