from apps.connectors.integrations import Template
from apps.connectors.integrations.vendors.vendor import Vendors

from .v1 import NozomiVantageV1TemplateVersion


class NozomiVantageTemplate(Template):
    id = "nozomi_vantage"
    name = "Nozomi Vantage"
    category = Template.Category.OT_SECURITY
    versions = {
        NozomiVantageV1TemplateVersion.id: NozomiVantageV1TemplateVersion(),
    }
    vendor = Vendors.NOZOMI
