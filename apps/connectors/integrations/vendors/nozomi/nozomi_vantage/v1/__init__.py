from apps.connectors.integrations import TemplateVersion

from .connection import NozomiVantageV1Config, NozomiVantageV1Connection
from .integration import NozomiVantageV1Integration
from .settings import NozomiVantageV1Settings


class NozomiVantageV1TemplateVersion(TemplateVersion):
    integration = NozomiVantageV1Integration
    id = "v1"
    name = "v1"
    config_model = NozomiVantageV1Config
    connection_model = NozomiVantageV1Connection
    settings_model = NozomiVantageV1Settings
